# SampleBreakDownFacadeService 业务设计说明文档

## 1. 系统概述

### 1.1 业务背景
SampleBreakDownFacadeService是SGS集团OTS Notes系统中负责样品分解业务的核心服务，基于门面模式(Facade Pattern)和责任链模式(Chain of Responsibility)设计，提供统一的样品分解处理入口。

### 1.2 业务目标
- **样品关系管理**：支持原样、子样、混样、共享样等多种样品类型的分解和关系维护
- **测试矩阵维护**：管理样品与测试项目之间的关联关系
- **分包数据同步**：支持SLIM分包系统的数据同步
- **事务性数据操作**：确保样品分解过程中的数据一致性

### 1.3 架构特点
- **重构设计**：基于现有业务重构，支持新旧版本平滑切换
- **组件化分离**：验证、处理、上下文管理职责分离
- **可扩展性**：支持未来业务需求的扩展

## 2. 核心架构设计

### 2.1 系统架构图

```mermaid
graph TB
    A[SampleBreakDownFacadeService<br/>门面服务] --> B[SampleBreakDownValidator<br/>验证器]
    A --> C[SampleBreakDownProcessor<br/>处理器]
    A --> D[SampleBreakDownContext<br/>上下文]
    
    B --> E[基础参数验证]
    B --> F[订单信息验证]
    B --> G[操作类型验证]
    B --> H[报告状态验证]
    B --> I[样品处理验证]
    B --> J[测试矩阵预处理]
    
    C --> K[数据加载构造]
    C --> L[NC状态验证]
    C --> M[样品数据处理]
    C --> N[测试矩阵处理]
    C --> O[SLIM分包处理]
    C --> P[数据库事务操作]
    C --> Q[后处理操作]
    
    D --> R[输入数据管理]
    D --> S[中间数据缓存]
    D --> T[输出数据整合]
```

### 2.2 组件职责

| 组件 | 职责描述 | 关键类 |
|------|----------|---------|
| **门面服务** | 统一业务入口，协调验证器和处理器 | SampleBreakDownFacadeService |
| **验证器** | 业务规则验证，确保数据合法性 | SampleBreakDownValidator |
| **处理器** | 核心业务逻辑处理 | SampleBreakDownProcessor |
| **上下文** | 数据传递和状态管理 | SampleBreakDownContext |

## 3. 核心数据模型

### 3.1 请求对象结构

**SampleBreakDownReq (样品分解请求)**:
```java
public class SampleBreakDownReq {
    private String orderNo;           // 订单号
    private String userName;          // 用户名
    private List<TestSampleReq> samples;      // 样品列表
    private List<SampleGroupReq> groups;      // 样品组
    private List<TestMatrixReq> matrixs;      // 测试矩阵
    private List<SlimMappingReq> slimMapping; // SLIM映射
}
```

**TestSampleReq (测试样品请求)**:
```java
public class TestSampleReq {
    private String sampleId;         // 样品ID
    private String sampleParentId;   // 父样品ID
    private String sampleNo;         // 样品编号
    private String sampleDesc;       // 样品描述
    private Integer sampleType;      // 样品类型
    private Integer sampleSeq;       // 样品序号
    private Boolean noTest;          // 是否不测试（NC状态）
    private String color;            // 颜色
    private String endUse;           // 用途
    private String composition;      // 成分
}
```

### 3.2 样品类型枚举

**SampleType (样品类型)**:
- **OriginalSample(101)**: 原样 - 最初样品，所有其他样品的根源
- **Sample(102)**: 子样 - 从原样分解出来的样品
- **SubSample(103)**: 子子样 - 从子样进一步分解的样品
- **MixSample(104)**: 混样 - 多个样品的混合体
- **ShareSample(105)**: 共享样 - 在多个测试间共享的样品

### 3.3 样品类型父子关系规则

| 子样品类型 | 允许的父样品类型 | 业务含义 |
|-----------|----------------|----------|
| Sample(102) | OriginalSample(101) | 子样只能从原样分解 |
| SubSample(103) | Sample(102), ShareSample(105) | 子子样从子样或共享样分解 |
| ShareSample(105) | OriginalSample(101) | 共享样只能基于原样 |
| MixSample(104) | 任何类型 | 混样可以混合任何样品 |

## 4. 核心业务流程

### 4.1 整体业务流程图

```mermaid
flowchart TD
    A[接收样品分解请求] --> B[验证阶段]
    B --> C[处理阶段]
    C --> D[返回处理结果]
    
    B --> B1[基础参数验证]
    B1 --> B2[订单信息验证]
    B2 --> B3[操作类型验证]
    B3 --> B4[报告状态验证]
    B4 --> B5[样品处理验证]
    B5 --> B6[测试矩阵预处理]
    
    C --> C1[数据加载和构造]
    C1 --> C2[NC状态验证]
    C2 --> C3[样品数据处理]
    C3 --> C4[测试矩阵处理]
    C4 --> C5[SLIM子承包商处理]
    C5 --> C6[数据库事务操作]
    C6 --> C7[后处理操作]
```

### 4.2 验证阶段详细流程

#### 4.2.1 基础参数验证
- **样品重复性验证**：检查样品编号是否重复
- **样品树结构验证**：验证样品父子关系的正确性
- **必填字段验证**：确保关键字段不为空

#### 4.2.2 订单信息验证
- **订单存在性验证**：确认订单在系统中存在
- **订单状态验证**：检查订单状态是否允许操作

#### 4.2.3 操作类型验证
- **NewSubContract检查**：特定操作类型的限制
- **权限验证**：确保用户有执行权限

#### 4.2.4 报告状态验证
- **允许的报告状态**：只有New、Combined、Draft状态允许操作
- **状态转换规则**：确保状态转换的合法性

#### 4.2.5 样品处理验证
- **重复样品验证**：检查重复样品问题
- **样品排序计算**：重新计算样品序号
- **混样编号重置**：重置混样的编号规则

#### 4.2.6 测试矩阵预处理
- **测试线ID提取**：提取请求中的测试线标识
- **测试线状态验证**：确认测试线是否已确认且有矩阵信息

### 4.3 处理阶段详细流程

#### 4.3.1 数据加载和构造
- **旧数据加载**：从数据库加载现有样品、矩阵等数据
- **映射关系构建**：建立各种数据间的映射关系
- **上下文数据填充**：将数据设置到处理上下文中

#### 4.3.2 NC状态验证
- **NC样品检查**：验证标记为NC（Not Test）的样品
- **矩阵关联检查**：确保NC样品没有关联的测试矩阵
- **父子关系检查**：验证NC样品的父子关系

#### 4.3.3 样品数据处理
- **原样处理**：处理OriginalSample类型样品
- **子样处理**：处理Sample/SubSample类型样品
- **混样处理**：处理MixSample类型样品的分组逻辑
- **共享样处理**：处理ShareSample类型样品的共享逻辑

#### 4.3.4 测试矩阵处理
- **矩阵关系构建**：建立样品与测试项目的关联
- **矩阵状态管理**：管理矩阵的激活状态
- **PP关系处理**：处理Protocol Package相关关系

#### 4.3.5 SLIM子承包商处理
- **SLIM数据验证**：验证SLIM分包数据的有效性
- **SLIM数据构造**：构造SLIM子承包商数据
- **分包关系维护**：维护分包与样品的关系

#### 4.3.6 数据库事务操作
- **事务管理**：确保所有数据库操作的原子性
- **数据持久化**：将处理结果保存到数据库
- **异常回滚**：发生错误时的数据回滚机制

## 5. 关键业务规则

### 5.1 样品类型验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **父子关系** | 子样(102)和共享样(105)的父样必须是原样(101) | SampleType.equals(parentSampleType, childSampleType)，在checkSampleTree方法中验证 | 拒绝请求，返回错误信息："未找到对应的sample(X) 的父样" |
| **类型限制** | 子子样(103)只能从子样(102)或共享样(105)分解 | 在SampleType.equals方法中检查父子关系合法性 | 拒绝请求，返回错误信息："未找到对应的sample(X) 的父样" |
| **混样规则** | 混样(104)可以是任何样品类型的组合 | 无特殊限制，但需要验证组成样品的有效性和至少包含两个样品 | 拒绝请求："至少选择两个样品才能混样" |
| **样品类型有效性** | 样品类型必须是有效的枚举值 | SampleType.findType(sample.getSampleType()) != null | 拒绝请求："当前SampleNo(X)，SampleType类型无效" |

### 5.2 NC状态验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **原样NC限制** | 原样不能设置为NoTest | sampleType == SampleType.OriginalSample && sample.getNoTest() == true，在checkSampleNc方法中验证 | 拒绝请求："当前原样SampleNo(X)不能NoTest" |
| **矩阵关联检查** | 已有Matrix的样品不能设置NoTest | oldMatrixSampleIds.containsKey(sample.getSampleId()) && sample.getNoTest() == true，在处理器中验证 | 拒绝请求："当前Sample(X)已Not Test，无法Matrix" |
| **子样NC限制** | 有子样的样品不能设置NoTest | currentDBHasChildSample \|\| currentParamDataHasChildSample，同时检查数据库中和请求参数中的子样关系 | 拒绝请求："当前SampleNo(X)存在子样不能NoTest" |
| **混样NC限制** | 混样不能设置为NoTest | sampleType == SampleType.MixSample && sample.getNoTest() == true | 拒绝请求："Mix样不能NoTest" |

### 5.3 测试矩阵验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **NoTest样品** | NoTest样品不能建立Matrix | sample.getNoTest() && context.getOldMatrixSampleIds().containsKey(sample.getSampleId())，在处理器中验证 | 拒绝请求："当前Sample(X)已Not Test，无法Matrix" |
| **TL状态检查** | 测试线已确认但没有matrix信息 | isTestLineConfirmed && !hasMatrixInfo，在isInvalidTestLineState方法中验证 | 拒绝请求："Please select at least one sample because TL has been confirmed or limit already exits" |
| **矩阵关联性** | 确保矩阵与样品的正确关联 | 验证TestMatrixReq中的sampleId有效性和存在性 | 拒绝请求："无效的样品关联" |
| **耐互染色测试线限制** | 耐互染色TestLine只允许Assign Mix样品 | 检查测试线类型和样品类型的匹配关系 | 拒绝请求："耐互染色TestLine只允许Assign Sample类型为Mix的样品" |

### 5.4 报告状态验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **允许状态** | 只有New(201)、Draft(204)、Combined(206)状态允许操作 | ReportStatus.check(oldReport.getReportStatus(), ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)，在验证器中检查 | 拒绝请求："当前Report[X] 的状态为New、Combin、Draft才允许回传" |
| **状态转换** | 确保状态转换的合法性 | 检查当前状态到目标状态的转换规则 | 拒绝请求："非法状态转换" |
| **报告存在性** | 确保报告存在 | oldReport != null | 拒绝请求："未找到当前OrderNo [X] 下的Report 信息" |

### 5.5 SLIM分包验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **样品存在性** | 传入的samples必须存在 | 检查sampleId在数据库中的存在性 | 拒绝请求："样品不存在" |
| **SLIM映射** | slimMapping中的sampleId必须有效 | 验证SlimMappingReq中的sampleId | 拒绝请求："无效的SLIM映射" |
| **数据完整性** | SLIM样品数据不能为空 | 检查slimSampleIds的非空性 | 拒绝请求："SLIM样品不能为空" |

### 5.6 订单和操作类型验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **订单存在性** | 订单必须存在 | orderMapper.getOrderInfo(orderNo) != null，在验证器中检查 | 拒绝请求："当前OrderNo [X] 不存在" |
| **操作类型限制** | NewSubContract操作类型不允许此操作 | OperationType.check(orderInfo.getOperationType(), OperationType.NewSubContract)，在验证器中检查 | 拒绝请求："当前OrderNo [X] 不能此操作" |

### 5.7 样品重复性和完整性验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **样品编号重复** | 样品编号不能重复 | checkSampleRepeat方法检查样品编号的唯一性 | 拒绝请求："Sample No[ X,Y,Z ] can't be repeat" |
| **必填字段验证** | 关键字段不能为空 | 检查orderNo、samples等必填字段 | 拒绝请求："请求的OrderNo/samples不能为空" |
| **样品ID有效性** | 新样品ID必须合法 | validateNewSampleIds方法验证样品ID格式和唯一性 | 拒绝请求："无效的样品ID" |

### 5.8 混样和共享样特殊规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **混样最小数量** | 混样至少需要两个组成样品 | sampleGroups.size() >= 2，在混样处理逻辑中验证 | 拒绝请求："至少选择两个样品才能混样" |
| **混样编号生成** | 混样编号由组成样品编号用"+"连接 | StringUtils.join(sampleNos.values(), "+")，在resetMixSampleNo方法中处理 | 自动生成混样编号 |
| **共享样父样限制** | 共享样只能基于原样创建 | 检查共享样的父样品类型必须是OriginalSample | 拒绝请求："共享样只能基于原样创建" |
| **重复测点检查** | TestLine上不能出现重复的测点 | 检查混样组合的重复性，在checkMainMaterial等方法中验证 | 拒绝请求："校验失败！TestLine上不能出现重复的测点" |

### 5.9 数据一致性验证规则

| 规则类别 | 具体规则 | 验证逻辑 | 违规后果 |
|---------|----------|----------|----------|
| **样品状态一致性** | 样品的Active状态必须一致 | checkSampleActiveOrNC方法检查样品的激活状态 | 拒绝请求："Sample has InActive or NC!" |
| **矩阵状态一致性** | 矩阵关系必须保持一致 | 验证样品与测试矩阵的关联关系 | 拒绝请求："矩阵关系不一致" |
| **语言数据一致性** | 多语言样品信息必须完整 | prepareSampleLanguageData方法处理样品语言映射 | 自动补充缺失的语言数据 |

## 6. 混样和共享样处理逻辑

### 6.1 混样处理流程

```mermaid
flowchart TD
    A[检测混样请求] --> B[验证混样组数量≥2]
    B --> C[检查混样不能NoTest]
    C --> D[验证组成样品有效性]
    D --> E[检查组成样品不能是混样]
    E --> F[构建样品组关系]
    F --> G[生成混样编号]
    G --> H[保存混样数据]
```

### 6.2 混样业务规则

| 验证项 | 业务规则 | 实现逻辑 |
|--------|----------|----------|
| **最小样品数** | 至少选择两个样品才能混样 | sampleGroups.size() > 1 |
| **NoTest限制** | 混样不能设置为NoTest | mixSample.noTest != true |
| **样品类型限制** | 混样不能再做混样 | 检查组成样品类型不能是MixSample |
| **编号生成** | 混样编号由组成样品编号用"+"连接 | StringUtils.join(sampleNos.values(), "+") |

### 6.3 共享样处理流程

```mermaid
flowchart TD
    A[检测共享样请求] --> B[验证父样是原样]
    B --> C[检查共享样品组]
    C --> D[验证原样未NC]
    D --> E[构建共享关系]
    E --> F[更新样品类型为ShareSample]
    F --> G[保存共享样数据]
```

## 7. 数据库事务管理

### 7.1 事务边界

**事务范围**：
- 样品数据的增删改
- 样品组关系的维护
- 测试矩阵的更新
- PP关系的管理
- SLIM分包数据的同步

**事务策略**：
- 使用Spring声明式事务管理
- 发生任何异常时自动回滚
- 确保数据的一致性和完整性

### 7.2 数据操作分类

| 操作类型 | 涉及表 | 操作描述 |
|---------|--------|----------|
| **样品操作** | tb_test_sample | 新增、更新、删除样品记录 |
| **样品组操作** | tb_test_sample_group | 维护混样和共享样的组关系 |
| **矩阵操作** | tb_test_matrix | 管理样品与测试的关联关系 |
| **PP关系操作** | tre_pp_sample_relationship | 管理Protocol Package关系 |
| **语言数据操作** | tb_test_sample_lang | 处理多语言样品信息 |
| **SLIM数据操作** | tb_slim_subcontract | 同步SLIM分包数据 |

## 8. 异常处理机制

### 8.1 异常分类

| 异常类型 | 触发条件 | 处理方式 | 用户提示 |
|---------|----------|----------|----------|
| **参数验证异常** | 请求参数不合法 | 直接返回错误信息 | 具体的参数错误说明 |
| **业务规则异常** | 违反业务约束 | 记录日志，返回业务错误 | 业务规则说明 |
| **数据访问异常** | 数据库操作失败 | 事务回滚，记录错误日志 | "系统繁忙，请稍后重试" |
| **系统异常** | 未预期的系统错误 | 事务回滚，记录完整堆栈 | "系统错误，请联系管理员" |

### 8.2 异常处理流程

```mermaid
flowchart TD
    A[业务操作] --> B{是否发生异常}
    B -->|是| C[记录异常日志]
    C --> D{是否业务异常}
    D -->|是| E[返回业务错误信息]
    D -->|否| F[事务回滚]
    F --> G[返回系统错误信息]
    B -->|否| H[正常返回结果]
```

## 9. 性能优化策略

### 9.1 数据加载优化
- **批量查询**：减少数据库访问次数
- **索引优化**：确保关键查询字段有合适索引
- **缓存策略**：对频繁访问的配置数据进行缓存

### 9.2 内存使用优化
- **延迟加载**：按需加载相关数据
- **对象池**：重用常用对象减少GC压力
- **数据分页**：大批量数据的分页处理

### 9.3 并发控制
- **乐观锁**：使用版本号控制并发更新
- **数据库锁**：关键业务使用数据库行锁
- **分布式锁**：跨服务的数据一致性控制

## 10. 监控和日志

### 10.1 业务监控指标
- **处理成功率**：样品分解操作的成功率
- **响应时间**：各阶段处理时间统计
- **错误分布**：各类异常的发生频率
- **并发量**：同时处理的请求数量

### 10.2 日志记录策略

| 日志级别 | 记录内容 | 用途 |
|---------|----------|------|
| **INFO** | 关键业务节点、处理结果 | 业务追踪 |
| **WARN** | 业务规则违反、数据不一致 | 业务告警 |
| **ERROR** | 系统异常、数据库错误 | 故障排查 |
| **DEBUG** | 详细处理过程、变量状态 | 开发调试 |

## 11. 扩展性设计

### 11.1 新样品类型支持
- **枚举扩展**：在SampleType中添加新类型
- **验证器扩展**：实现新类型的验证逻辑
- **处理器扩展**：添加新类型的处理逻辑

### 11.2 新业务规则支持
- **责任链模式**：新增验证处理器
- **策略模式**：为不同场景实现不同策略
- **配置驱动**：通过配置文件控制业务规则

### 11.3 新数据源集成
- **适配器模式**：为新数据源实现适配器
- **工厂模式**：统一数据源创建和管理
- **依赖注入**：便于不同环境的数据源切换

## 12. 版本兼容性

### 12.1 新旧版本切换
- **配置开关**：通过配置控制是否使用新版本
- **平滑迁移**：支持新旧版本并行运行
- **回滚机制**：出现问题时快速回滚到旧版本

### 12.2 API兼容性
- **向下兼容**：新版本API兼容旧版本调用
- **版本标识**：通过版本号区分不同API版本
- **废弃机制**：逐步废弃不推荐的API接口

## 13. 总结

SampleBreakDownFacadeService通过门面模式提供了统一的样品分解业务入口，采用责任链模式实现了验证和处理逻辑的解耦。系统支持多种样品类型的分解和关系管理，具有完善的业务规则验证、异常处理和事务管理机制。

**核心价值**：
1. **业务统一性**：提供统一的样品分解业务处理入口
2. **数据一致性**：确保复杂业务操作的事务完整性
3. **规则完整性**：全面的业务规则验证保证数据质量
4. **架构灵活性**：模块化设计支持业务扩展和维护

该服务作为OTS Notes系统的核心组件，为样品分解业务提供了稳定、可靠、可扩展的技术支撑。 